"""
Defines the state for the LangGraph graph.
"""

from typing import TypedDict, Dict, Any, Annotated

def merge_dicts(left: Dict[str, Any], right: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merges two dictionaries, with values from the right dictionary overwriting
    values from the left dictionary in case of duplicate keys.
    """
    return {**left, **right}

class GraphState(TypedDict):
    """
    Represents the state of our graph.
    
    Attributes:
        task_results: A dictionary to store the output of each task.
        live_objects: A dictionary to store instantiated environment objects.
    """
    task_results: Annotated[Dict[str, Any], merge_dicts]
    live_objects: Dict[str, Any]
