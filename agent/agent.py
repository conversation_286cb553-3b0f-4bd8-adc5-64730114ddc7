from typing import Dict, Any
import os

from dotenv import load_dotenv

from langchain_openai import AzureChatOpenAI, ChatOpenAI
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage

from agent.compiler import Compiler, ENVIRONMENT_REGISTRY
from agent.dsl.base import generate_dsl_schema
from agent.graph_builder import LangGraphBuilder

class Agent:
    """
    The main agent class that uses a self-correction loop to execute tasks.
    It compiles the DSL to a native LangGraph graph for execution.
    """
    def __init__(self, max_retries: int = 3, model_name: str = "gemini-2.5-flash"):
        self.max_retries = max_retries
        load_dotenv() # Load environment variables from .env file
        
        dsl_schema = generate_dsl_schema(ENVIRONMENT_REGISTRY)
        system_prompt = (
            "You are an AI agent that generates Python DSL code to accomplish tasks.\n"
            "Adhere to the following rules:\n"
            "1. Only use the provided classes and methods. Do NOT use any other Python functions or libraries, including 'print'.\n"
            "2. Generate only the DSL code. Do NOT include any conversational text, explanations, or comments.\n"
            "3. Think of this as a batch execution. You will not see intermediate outputs. Focus on the same result.\n"
            "4. Assign the results of tool calls to variables for later use if needed.\n"
        ) + dsl_schema
        self.llm = ChatOpenAI(
            model=model_name,
            temperature=0.7,
            api_key=os.getenv("GEMINI_API_KEY"),
            base_url="https://generativelanguage.googleapis.com/v1beta/openai/",
            # stop_sequences=stop_sequences,
        )
        
        self.system_message = SystemMessage(content=system_prompt)

    async def run(self, task: str) -> Dict[str, Any]:
        """
        Runs a task using the self-correction loop.
        """
        last_error = ""
        for i in range(self.max_retries):
            print(f"--- ATTEMPT {i + 1} of {self.max_retries} ---")
            
            messages = [self.system_message, HumanMessage(content=f"Task: {task}")]
            if last_error:
                messages.append(HumanMessage(content=last_error))

            print("LLM: Generating plan for task:", task)
            # Hardcoded plan for testing if/else
            plan = """
env = DockerEnvironment()
status = env.run(command="echo success") # Simulate a command that returns 'success'

if status == "success":
    print_success = env.run(command="echo Task succeeded!")
else:
    print_failure = env.run(command="echo Task failed!")

env.close()
"""
            
            print("--- GENERATED PLAN ---")
            print(plan)
            print("----------------------")

            try:
                compiler = Compiler()
                graph, task_map = compiler.compile(plan)
                
                builder = LangGraphBuilder(task_map)
                app = builder.build(graph)
                
                # Initial state for the graph
                initial_state = {"task_results": {}, "live_objects": {}}
                
                # Invoke the graph asynchronously
                final_state = await app.ainvoke(initial_state)
                
                print("--- PLAN SUCCEEDED ---")
                return final_state["task_results"]

            except (SyntaxError, NameError, AttributeError, Exception) as e:
                last_error = f"The previous plan failed with this error: {e}. Please generate a new, corrected plan."
                print(f"--- PLAN FAILED: {e} ---")
        
        raise RuntimeError(f"Failed to execute task after {self.max_retries} attempts.")
