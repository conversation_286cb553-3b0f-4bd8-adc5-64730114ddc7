import asyncio
from pprint import pprint

from agent.agent import Agent
from agent.dsl.base import BaseEnvironment

async def main():
    """
    The main entrypoint for the agent.
    """
    print("---" + " CREATING AGENT" + " ---")
    agent = Agent()
    
    task = "List the files in the /data directory, get running processes, and create a test file."
    
    try:
        final_results = await agent.run(task)
        print("\n" + "---" + " FINAL RESULTS" + " ---")
        # Filter out the environment object itself for cleaner printing
        printable_results = {k: v for k, v in final_results.items() if not isinstance(v, BaseEnvironment)}
        pprint(printable_results)
    except Exception as e:
        print("\n" + "---" + " AGENT FAILED" + " ---")
        print(e)

if __name__ == "__main__":
    asyncio.run(main())
