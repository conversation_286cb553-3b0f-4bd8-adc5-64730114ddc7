
import ast
import graphlib
from graphlib import <PERSON><PERSON><PERSON>orter
from typing import Dict, <PERSON>, <PERSON>, <PERSON><PERSON>

from agent.environments.docker_env import DockerEnvironment

ENVIRONMENT_REGISTRY = {
    "DockerEnvironment": DockerEnvironment,
}

class Compiler:
    """
    A stateless compiler that parses object-oriented Python DSL and compiles it
    into a dependency graph (DAG).
    """

    def compile(self, code_string: str) -> <PERSON>ple[Dict[str, Set[str]], Dict[str, Any]]:
        """
        Takes a string of Python DSL code and returns a dependency graph and a task map.
        """
        tree = ast.parse(code_string)
        graph: Dict[str, Set[str]] = {}
        task_map: Dict[str, Dict[str, Any]] = {}
        all_tasks: list[str] = []

        def _process_ast_nodes(nodes: list[ast.AST], current_graph: Dict[str, Set[str]], current_task_map: Dict[str, Dict[str, Any]], current_all_tasks: list[str]) -> <PERSON><PERSON>[str | None, str | None]:
            """
            Processes a list of AST nodes, adding them to the graph and task map.
            Returns the first and last task names processed in this block.
            """
            if not nodes: return None, None

            first_task_in_block = None
            last_task_in_block = None
            
            # Create a temporary graph for this block to manage internal dependencies
            block_graph: Dict[str, Set[str]] = {}
            block_task_map: Dict[str, Dict[str, Any]] = {}
            block_all_tasks: list[str] = []

            for sub_node in nodes:
                sub_task_name: str = ""
                sub_call_node: ast.Call | None = None

                if isinstance(sub_node, ast.Assign) and isinstance(sub_node.value, ast.Call):
                    sub_task_name = sub_node.targets[0].id
                    sub_call_node = sub_node.value
                elif isinstance(sub_node, ast.Expr) and isinstance(sub_node.value, ast.Call):
                    sub_task_name = f"expr_{sub_node.lineno}_{sub_node.col_offset}"
                    sub_call_node = sub_node.value
                elif isinstance(sub_node, ast.If):
                    # Recursively process nested if statements
                    nested_if_first, nested_if_last = _process_ast_nodes([sub_node], block_graph, block_task_map, block_all_tasks)
                    if not first_task_in_block: first_task_in_block = nested_if_first
                    last_task_in_block = nested_if_last
                    continue
                else:
                    # Skip unsupported nodes for now, or raise error
                    continue
                
                if not first_task_in_block: first_task_in_block = sub_task_name
                last_task_in_block = sub_task_name

                block_all_tasks.append(sub_task_name)
                sub_dependencies: Set[str] = set()

                if isinstance(sub_call_node.func, ast.Name):
                    env_class_name = sub_call_node.func.id
                    if env_class_name in ENVIRONMENT_REGISTRY:
                        block_task_map[sub_task_name] = {
                            "type": "instantiation",
                            "class_name": env_class_name,
                            "args": sub_call_node.args,
                            "keywords": sub_call_node.keywords,
                        }
                    else:
                        raise NameError(f"Environment '{env_class_name}' not found.")

                elif isinstance(sub_call_node.func, ast.Attribute):
                    method_name = sub_call_node.func.attr
                    env_var_name = sub_call_node.func.value.id
                    sub_dependencies.add(env_var_name)
                    block_task_map[sub_task_name] = {
                        "type": "method_call",
                        "env_var": env_var_name,
                        "method": method_name,
                        "args": sub_call_node.args,
                        "keywords": sub_call_node.keywords,
                    }

                for arg in sub_call_node.args:
                    if isinstance(arg, ast.Name):
                        sub_dependencies.add(arg.id)
                for kw in sub_call_node.keywords:
                    if isinstance(kw.value, ast.Name):
                        sub_dependencies.add(kw.value.id)
                
                block_graph[sub_task_name] = sub_dependencies

                # Add sequential dependency within the block
                if len(block_all_tasks) > 1:
                    block_graph[sub_task_name].add(block_all_tasks[-2])
            
            # Merge block_graph and block_task_map into the main graph and task_map
            current_graph.update(block_graph)
            current_task_map.update(block_task_map)
            current_all_tasks.extend(block_all_tasks)

            return first_task_in_block, last_task_in_block

        # First pass: discover all tasks and their direct data dependencies
        for node in ast.walk(tree):
            task_name: str = ""
            call_node: ast.Call | None = None

            if isinstance(node, ast.Assign) and isinstance(node.value, ast.Call):
                task_name = node.targets[0].id
                call_node = node.value
            elif isinstance(node, ast.Expr) and isinstance(node.value, ast.Call):
                task_name = f"expr_{node.lineno}_{node.col_offset}"
                call_node = node.value

            if not task_name or not call_node:
                continue

            if not task_name and not isinstance(node, ast.If): # Handle ast.If nodes
                continue

            if isinstance(node, ast.If):
                # Handle if statements
                condition_node = node.test
                if not isinstance(condition_node, ast.Compare) or \
                   not isinstance(condition_node.left, ast.Name) or \
                   len(condition_node.ops) != 1 or \
                   not isinstance(condition_node.comparators[0], (ast.Constant, ast.Name)):
                    raise SyntaxError("Unsupported if condition format. Only 'var op value' is supported.")

                op = condition_node.ops[0]
                if isinstance(op, ast.Eq): op_str = "=="
                elif isinstance(op, ast.NotEq): op_str = "!="
                elif isinstance(op, ast.Lt): op_str = "<"
                elif isinstance(op, ast.LtE): op_str = "<="
                elif isinstance(op, ast.Gt): op_str = ">"
                elif isinstance(op, ast.GtE): op_str = ">="
                else:
                    raise SyntaxError(f"Unsupported comparison operator: {type(op).__name__}")

                condition_var = condition_node.left.id
                if isinstance(condition_node.comparators[0], ast.Constant):
                    condition_value = condition_node.comparators[0].value
                else: # Must be ast.Name
                    condition_value = condition_node.comparators[0].id

                # Generate a unique name for this conditional node
                conditional_node_name = f"if_cond_{node.lineno}_{node.col_offset}"
                all_tasks.append(conditional_node_name)
                
                # Process if body
                if_body_first_task, if_body_last_task = _process_ast_nodes(node.body, graph, task_map, all_tasks)

                # Process else body
                else_body_first_task, else_body_last_task = _process_ast_nodes(node.orelse, graph, task_map, all_tasks)

                # Determine the node that follows the if/else block
                # This is tricky and depends on the next statement in the main flow
                # For now, we'll assume a simple sequential flow after the if/else
                # A more robust solution would require a full AST walk to find the next statement
                next_node_after_if = f"after_if_{node.lineno}_{node.col_offset}" # Placeholder
                all_tasks.append(next_node_after_if)
                task_map[next_node_after_if] = {"type": "placeholder"} # Placeholder node

                task_map[conditional_node_name] = {
                    "type": "conditional_branch",
                    "condition_var": condition_var,
                    "operator": op_str,
                    "value": condition_value,
                    "if_branch_start": if_body_first_task,
                    "else_branch_start": else_body_first_task,
                    "next_node_after_if": next_node_after_if,
                }
                graph[conditional_node_name] = {condition_var} # Condition depends on the variable

                # Add dependencies from the end of branches to the node after if/else
                if if_body_last_task: graph[next_node_after_if].add(if_body_last_task)
                if else_body_last_task: graph[next_node_after_if].add(else_body_last_task)

                # Ensure the conditional node is connected to the main flow
                # This assumes the if statement is part of a sequential flow
                # We need to find the previous task in the main flow and connect it to the conditional node
                # This is a simplification and needs a more robust AST traversal for general cases
                if len(all_tasks) > 1 and all_tasks[-2] != conditional_node_name: # Check if there's a previous task
                    graph[conditional_node_name].add(all_tasks[-2])

                continue

            all_tasks.append(task_name)
            dependencies: Set[str] = set()

            # Get task info and direct data dependencies
            if isinstance(call_node.func, ast.Name):
                env_class_name = call_node.func.id
                if env_class_name in ENVIRONMENT_REGISTRY:
                    task_map[task_name] = {
                        "type": "instantiation",
                        "class_name": env_class_name,
                        "args": call_node.args,
                        "keywords": call_node.keywords,
                    }
                else:
                    raise NameError(f"Environment '{env_class_name}' not found.")

            elif isinstance(call_node.func, ast.Attribute):
                method_name = call_node.func.attr
                env_var_name = call_node.func.value.id
                dependencies.add(env_var_name)
                task_map[task_name] = {
                    "type": "method_call",
                    "env_var": env_var_name,
                    "method": method_name,
                    "args": call_node.args,
                    "keywords": call_node.keywords,
                }

            for arg in call_node.args:
                if isinstance(arg, ast.Name):
                    dependencies.add(arg.id)
            for kw in call_node.keywords:
                if isinstance(kw.value, ast.Name):
                    dependencies.add(kw.value.id)
            
            graph[task_name] = dependencies

        # Second pass: add special dependencies for `close` methods
        for task_name in all_tasks:
            task_info = task_map.get(task_name, {})
            if task_info.get("type") == "method_call" and task_info.get("method") == "close":
                env_var_to_close = task_info["env_var"]
                for other_task in all_tasks:
                    if other_task == task_name: continue
                    other_task_info = task_map.get(other_task, {})
                    if other_task_info.get("type") == "method_call" and other_task_info.get("env_var") == env_var_to_close:
                        graph[task_name].add(other_task)

        return graph, task_map
