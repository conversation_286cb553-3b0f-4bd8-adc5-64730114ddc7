"""
Builds a LangGraph graph from the output of our custom DSL compiler.
"""

import ast
import asyncio
from typing import Dict, Any, List, Tuple, Set
from langgraph.graph import StateGraph, END
from graphlib import TopologicalSorter

from agent.graph_state import GraphState
from agent.compiler import Compiler, ENVIRONMENT_REGISTRY

# Helper function to resolve arguments from the current state
def _resolve_args(call_args: List[ast.AST], call_keywords: List[ast.keyword], state: GraphState) -> Tuple[List[Any], Dict[str, Any]]:
    args = []
    kwargs = {}
    task_results = state["task_results"]

    for arg in call_args:
        if isinstance(arg, ast.Name):
            args.append(task_results[arg.id])
        elif isinstance(arg, ast.Constant):
            args.append(arg.value)
        else:
            raise TypeError(f"Unsupported positional argument type: {type(arg)}")

    for kw in call_keywords:
        if isinstance(kw.value, ast.Name):
            kwargs[kw.arg] = task_results[kw.value.id]
        elif isinstance(kw.value, ast.Constant):
            kwargs[kw.arg] = kw.value.value
        else:
            raise TypeError(f"Unsupported keyword argument value type: {type(kw.value)}")
    return args, kwargs

class LangGraphBuilder:
    """
    Dynamically builds and compiles a LangGraph executable app
    from a compiled DSL plan.
    """
    def __init__(self, task_map: Dict[str, Any]):
        self.task_map = task_map
        self.workflow = StateGraph(GraphState)

    def _create_node_function(self, task_name: str):
        """
        Creates a function suitable for a LangGraph node that executes a single task.
        """
        task_info = self.task_map[task_name]

        async def node_func(state: GraphState) -> Dict[str, Any]:
            print(f"--- EXECUTING NODE: {task_name} ---")
            args, kwargs = _resolve_args(task_info["args"], task_info["keywords"], state)
            live_objects = state["live_objects"]
            
            if task_info["type"] == "instantiation":
                env_class = ENVIRONMENT_REGISTRY[task_info["class_name"]]
                instance = env_class(*args, **kwargs)
                live_objects[task_name] = instance
                return {"live_objects": live_objects, "task_results": {task_name: instance}}
            
            elif task_info["type"] == "method_call":
                env_var = task_info["env_var"]
                method_name = task_info["method"]
                instance = live_objects[env_var]
                method_to_call = getattr(instance, method_name)
                
                # Directly await the async method call
                result = await method_to_call(*args, **kwargs)
                return {"task_results": {task_name: result}}
            
            elif task_info["type"] == "conditional_branch":
                condition_var = task_info["condition_var"]
                operator = task_info["operator"]
                value = task_info["value"]

                # Evaluate the condition
                current_value = state["task_results"].get(condition_var)

                if operator == "==": return current_value == value
                elif operator == "!=": return current_value != value
                elif operator == "<": return current_value < value
                elif operator == ">": return current_value > value
                elif operator == "<=": return current_value <= value
                elif operator == ">=": return current_value >= value
                else: raise ValueError(f"Unknown operator: {operator}")

            return {}
        
        return node_func

    def build(self, graph: Dict[str, Set[str]]):
        """
        Builds the entire graph, adding all nodes and edges.
        """
        all_nodes = list(graph.keys())

        # Add all nodes to the graph
        for task_name in all_nodes:
            node_function = self._create_node_function(task_name)
            self.workflow.add_node(task_name, node_function)

        # Add all edges based on dependencies and conditional branches
        for node, preds in graph.items():
            task_info = self.task_map.get(node)
            if task_info and task_info["type"] == "conditional_branch":
                # This is a conditional node, add conditional edges
                branches = {}
                if task_info["if_branch_start"]:
                    branches["true"] = task_info["if_branch_start"]
                if task_info["else_branch_start"]:
                    branches["false"] = task_info["else_branch_start"]
                
                # Define the condition function for LangGraph
                def condition_func(state: GraphState) -> str:
                    # Evaluate the condition using the node_func logic
                    # This will return True or False based on the condition
                    condition_result = self._create_node_function(node)(state)
                    if condition_result:
                        return "if_branch"
                    else:
                        return "else_branch"

                self.workflow.add_conditional_edges(node, condition_func, {
                    "if_branch": task_info["if_branch_start"],
                    "else_branch": task_info["else_branch_start"],
                })

            if task_info and task_info["type"] == "conditional_branch":
                # This is a conditional node, add conditional edges
                branches = {}
                if task_info["if_branch_start"]:
                    branches["true"] = task_info["if_branch_start"]
                if task_info["else_branch_start"]:
                    branches["false"] = task_info["else_branch_start"]
                
                # Define the condition function for LangGraph
                def condition_func(state: GraphState) -> str:
                    # Evaluate the condition using the node_func logic
                    # This will return True or False based on the condition
                    condition_result = self._create_node_function(node)(state)
                    if condition_result:
                        return "if_branch"
                    else:
                        return "else_branch"

                self.workflow.add_conditional_edges(node, condition_func, {
                    "if_branch": task_info["if_branch_start"],
                    "else_branch": task_info["else_branch_start"],
                })

            else:
                # Regular node, add direct edges
                for pred in preds:
                    self.workflow.add_edge(pred, node)

        # Set the entry and end points
        entry_points = [n for n, deps in graph.items() if not deps]
        if not entry_points:
            raise ValueError("Graph has no entry points or is cyclic.")
        
        self.workflow.set_entry_point(entry_points[0])
        # For simplicity, if there are multiple entry points, create edges from the first
        for i in range(1, len(entry_points)):
            self.workflow.add_edge(entry_points[0], entry_points[i])

        # All nodes that have no successors lead to the end
        all_successors = {pred for preds in graph.values() for pred in preds}
        end_points = [n for n in all_nodes if n not in all_successors]
        for node in end_points:
            self.workflow.add_edge(node, END)

        return self.workflow.compile()