"""
This module defines the Docker environment for executing commands in containers.
"""

import asyncio
import docker
import os
from pathlib import Path
from typing import Optional

from agent.dsl.base import BaseEnvironment, env_tool

class DockerEnvironment(BaseEnvironment):
    """
    An environment for running commands inside a dedicated Docker container.
    The container is started upon instantiation (RAII pattern).
    """
    def __init__(self, image="compiler-agent-env"):
        try:
            self.docker_client = docker.from_env()
        except docker.errors.DockerException:
            print("ERROR: Docker is not running or not installed.")
            raise
        
        self.image = image
        self.container_id: Optional[str] = None
        self.host_data_dir = Path(os.getcwd()) / "data"
        self.host_data_dir.mkdir(exist_ok=True)
        
        # Start the container upon instantiation
        self._start_container()
        print(f"DockerEnvironment initialized in container {self.container_id[:12]}")

    def _start_container(self):
        """Starts a persistent Docker container."""
        container = self.docker_client.containers.run(
            image=self.image,
            command="tail -f /dev/null",  # Keep container alive
            volumes={str(self.host_data_dir): {"bind": "/data", "mode": "rw"}},
            working_dir="/data",
            detach=True,
            remove=True,  # auto-remove on stop
        )
        self.container_id = container.id

    @env_tool
    async def run(self, command: str) -> str:
        """Runs a command in the persistent Docker container."""
        if not self.container_id:
            raise RuntimeError("Container not running. The environment may have been closed.")

        def _exec_run():
            container = self.docker_client.containers.get(self.container_id)
            exit_code, output_bytes = container.exec_run(command)
            output = output_bytes.decode('utf-8').strip()
            if exit_code != 0:
                print(f"COMMAND FAILED (Exit Code: {exit_code}): {command}")
                output += f"\nERROR (Exit Code: {exit_code})"
            return output

        print(f"Running in container [{self.container_id[:12]}]: {command}")
        return await asyncio.to_thread(_exec_run)

    @env_tool
    async def close(self):
        """Stops the Docker container session."""
        if not self.container_id:
            print("No active container to close.")
            return

        def _stop_container():
            try:
                container = self.docker_client.containers.get(self.container_id)
                print(f"Stopping container {container.id[:12]}...")
                container.stop()
            except docker.errors.NotFound:
                print(f"Warning: Container {self.container_id[:12]} not found. It might have been removed already.")
        
        await asyncio.to_thread(_stop_container)
        print(f"Container {self.container_id[:12]} stopped.")
        self.container_id = None

    @env_tool
    async def run_stateless(self, command: str) -> str:
        """Runs a command in a new, ephemeral container that is removed after execution."""
        def _run_ephemeral():
            try:
                container_output = self.docker_client.containers.run(
                    image=self.image,
                    command=f'/bin/bash -c "{command}"',
                    volumes={str(self.host_data_dir): {"bind": "/data", "mode": "rw"}},
                    working_dir="/data",
                    remove=True,
                    stdout=True,
                    stderr=True,
                )
                return container_output.decode('utf-8').strip()
            except docker.errors.ContainerError as e:
                error_log = f"Stdout:\n{e.stdout.decode('utf-8')}\nStderr:\n{e.stderr.decode('utf-8')}"
                print(f"STATELESS COMMAND FAILED: {command}\n{error_log}")
                return error_log

        print(f"Running stateless command: {command}")
        return await asyncio.to_thread(_run_ephemeral)
