"""
This module defines the base classes and decorators for creating DSL environments and tools.
"""

import inspect
from typing import Dict, Type

def env_tool(func):
    """
    Decorator to mark a method within a BaseEnvironment class as a tool
    callable from the DSL.
    """
    func._is_env_tool = True
    return func

class BaseEnvironment:
    """
    Base class for all DSL environments.
    Environments are stateful containers for a set of related tools.
    """
    def __repr__(self):
        return f"<{self.__class__.__name__} Environment>"


def generate_dsl_schema(registry: Dict[str, Type[BaseEnvironment]]) -> str:
    """
    Generates a .pyi-style string of stubs for the registered environments.
    This uses introspection to build the schema dynamically.
    """
    schema_lines = [
        "# You can use the following Python classes and methods to construct your plan.",
        "# Do not use any other tools or import any libraries.\n"
    ]

    for name, env_class in registry.items():
        schema_lines.append(f"class {name}:")
        if env_class.__doc__:
            docstring = inspect.cleandoc(env_class.__doc__)
            # Corrected f-string using triple quotes
            schema_lines.append(f'''    """
    {docstring}
    ""''')

        # Add __init__ method
        try:
            init_sig = inspect.signature(env_class.__init__)
            schema_lines.append(f"    def __init__{init_sig}: ...\n")
        except ValueError:  # Handles cases like object.__init__ which has no signature
            pass

        # Add tool methods
        for method_name, method in inspect.getmembers(env_class, predicate=inspect.isfunction):
            if hasattr(method, '_is_env_tool'):
                method_sig = inspect.signature(method)
                schema_lines.append(f"    def {method_name}{method_sig}: ...")
                if method.__doc__:
                    docstring = inspect.cleandoc(method.__doc__)
                    # Corrected f-string using triple quotes
                    schema_lines.append(f'''        """
        {docstring}
        ""''')
        schema_lines.append("\n")

    return "\n".join(schema_lines)
