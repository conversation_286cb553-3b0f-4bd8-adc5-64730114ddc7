# GEMINI.md

## Project Overview

This project is a general-purpose AI Agent system based on the "LLM Compiler" architecture. It is designed to translate complex, high-level tasks into a structured, executable plan. The system leverages a Python-based Domain-Specific Language (DSL) to define these plans, which are then parsed into a Directed Acyclic Graph (DAG) for parallel execution.

The core philosophy is to separate the high-level planning capabilities of a Large Language Model (LLM) from the low-level execution details. This allows for a more robust, secure, and scalable system.

Key features include:
- **Python DSL:** A restricted Python environment for defining tasks and workflows.
- **LLM as a Tool:** The ability to use an LLM as a tool within the DSL to process information and make decisions.
- **Parallel Execution:** A compiler that analyzes the Python DSL, builds a dependency graph, and executes independent tasks in parallel using `asyncio`.
- **Stateful and Stateless Tools:** A unified tool system that can handle both simple, stateless tools (e.g., file I/O) and complex, stateful tools (e.g., SSH sessions, human-in-the-loop).
- **Sandboxed Execution:** All code is executed in a secure Docker container to prevent accidental or malicious damage to the host system.
- **Dynamic Error Correction:** A feedback loop that allows the LLM to correct its own plans based on execution errors.
- **Human-in-the-Loop:** A mechanism for the AI to request human input and for humans to intervene in the execution process.

## Building and Running

**TODO:** The build and run commands are not yet defined. This section should be updated with instructions on how to set up the environment, install dependencies, and run the agent.

## Development Conventions

- **Planning Language:** All tasks must be defined using the Python DSL. Direct execution of arbitrary Python code is not allowed.
- **Tool Definition:** All tools must be explicitly defined and registered with the system.
- **Error Handling:** Tools should raise exceptions on failure, which will be caught by the execution engine and fed back to the LLM for correction.
- **State Management:** Stateful tools should be managed through the Session Manager to ensure that their state is properly tracked and cleaned up.
