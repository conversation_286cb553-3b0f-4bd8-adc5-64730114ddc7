
import unittest
import ast
from graphlib import TopologicalSorter

from agent.compiler.compiler import Compiler

class TestCompiler(unittest.TestCase):

    def setUp(self):
        self.compiler = Compiler()

    def test_simple_dependency(self):
        """Tests a simple plan with one task depending on another."""
        plan = """
a = task_one()
b = task_two(input=a)
"""
        sorter = self.compiler.compile(plan)

        # First batch should be 'a'
        ready1 = sorter.get_ready()
        self.assertEqual(set(ready1), {'a'})
        sorter.done('a')

        # Second batch should be 'b'
        ready2 = sorter.get_ready()
        self.assertEqual(set(ready2), {'b'})
        sorter.done('b')

        self.assertFalse(sorter.is_active())

        # Also test the task_map
        self.assertIsInstance(self.compiler.task_map['a'], ast.Call)
        self.assertEqual(self.compiler.task_map['a'].func.id, 'task_one')

    def test_parallel_tasks(self):
        """Tests a plan with two independent tasks."""
        plan = """
a = task_one()
b = task_two()
"""
        sorter = self.compiler.compile(plan)
        ready_nodes = sorter.get_ready()
        
        self.assertEqual(set(ready_nodes), {'a', 'b'})

    def test_multiple_dependencies(self):
        """Tests a task that depends on two other tasks."""
        plan = """
a = task_one()
b = task_two()
c = task_three(in1=a, in2=b)
"""
        sorter = self.compiler.compile(plan)
        
        # First batch should be a and b
        ready_batch1 = sorter.get_ready()
        self.assertEqual(set(ready_batch1), {'a', 'b'})
        sorter.done(*ready_batch1)
        
        # Second batch should be c
        ready_batch2 = sorter.get_ready()
        self.assertEqual(set(ready_batch2), {'c'})

    def test_full_workflow_graph(self):
        """
        Tests the compilation of the full bioinformatics workflow,
        ensuring the dependency graph is correct.
        """
        full_plan = """
ref_genome = download_genome(genome_name="S_cerevisiae")
raw_data = download_sra_data(accession="SRR554369")
indexed_genome = index_genome_bwa(genome_path=ref_genome)
qc_report = run_fastqc(reads_path=raw_data)
aligned_sam = align_reads_bwa(genome_path=indexed_genome, reads_path=raw_data)
"""
        sorter = self.compiler.compile(full_plan)
        
        # First batch: downloads can run in parallel
        ready1 = sorter.get_ready()
        self.assertEqual(set(ready1), {'ref_genome', 'raw_data'})
        sorter.done(*ready1)
        
        # Second batch: indexing and QC can run in parallel
        ready2 = sorter.get_ready()
        self.assertEqual(set(ready2), {'indexed_genome', 'qc_report'})
        sorter.done(*ready2)
        
        # Final batch: alignment depends on the previous two
        ready3 = sorter.get_ready()
        self.assertEqual(set(ready3), {'aligned_sam'})
        sorter.done(*ready3)
        
        self.assertFalse(sorter.is_active())

if __name__ == '__main__':
    unittest.main()

    def test_non_assigning_call(self):
        """Tests a plan with a function call that is not assigned to a variable."""
        plan = """
a = task_one()
task_two(input=a)
"""
        sorter = self.compiler.compile(plan)

        # First batch should be 'a'
        ready1 = sorter.get_ready()
        self.assertEqual(set(ready1), {'a'})
        sorter.done('a')

        # Second batch should be the non-assigned call to task_two
        ready2 = sorter.get_ready()
        self.assertEqual(len(ready2), 1)
        task2_name = list(ready2)[0]
        self.assertTrue(task2_name.startswith('expr_'))
        
        # Check that the task map is correct for the generated name
        call_node = self.compiler.task_map[task2_name]
        self.assertEqual(call_node.func.id, 'task_two')

        sorter.done(task2_name)
        self.assertFalse(sorter.is_active())
