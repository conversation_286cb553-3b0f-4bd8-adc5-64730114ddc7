# Use a standard Ubuntu image as the base
FROM ubuntu:22.04

# Avoid interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Update package lists and install necessary dependencies
# - wget: for downloading files
# - sra-toolkit: for downloading data from NCBI SRA
# - default-jre: Java Runtime Environment, required by FastQC
# - unzip: for unzipping the FastQC package
RUN apt-get update && apt-get install -y \
    wget \
    sra-toolkit \
    bwa \
    gzip \
    default-jre \
    perl \
    unzip && \
    rm -rf /var/lib/apt/lists/*

# Download and install FastQC
RUN wget https://www.bioinformatics.babraham.ac.uk/projects/fastqc/fastqc_v0.12.1.zip -P /tmp
RUN unzip /tmp/fastqc_v0.12.1.zip -d /usr/local/
RUN chmod +x /usr/local/FastQC/fastqc

# Add FastQC to the system's PATH
ENV PATH=$PATH:/usr/local/FastQC

# Create a directory to be used for mounting the shared data volume
RUN mkdir /data

# Set the working directory inside the container
WORKDIR /data
